<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport"
          content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
  <link rel="stylesheet" href="css/bootstrap.min.css">
  <link rel="stylesheet" href="css/all.min.css">
  <link rel="stylesheet" href="css/style.css">
    <title>Brainwave</title>
</head>
<body>
<header>
    <nav class="navbar navbar-expand-lg navbar-transparent" id="mainNavbar">
        <div class="container">
            <a class="navbar-brand" href="#"><img src="assets/logo.svg" alt=""></a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNavAltMarkup" aria-controls="navbarNavAltMarkup" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNavAltMarkup">
                <div class="navbar-nav ms-auto mb-2 mb-lg-0 text-white">
                    <a class="nav-link p-2 p-lg-3" aria-current="page" href="#">Home</a>
                    <a class="nav-link p-2 p-lg-3" href="#">Features</a>
                    <a class="nav-link p-2 p-lg-3" href="#">Pricing</a>
                    <a class="nav-link p-2 p-lg-3" aria-disabled="true">Disabled</a>
                </div>
            </div>
        </div>
    </nav>
</header>
<main>
    <div class="hero-section d-flex align-items-center justify-content-center">
        <div class="hero-content text-center">
            <h6>Shared space in your town</h6>
            <h1>Rent desk space in a shared office environment</h1>
            <form class="search-form d-flex align-items-center justify-content-center gap-3 mb-4">
                <div class="search-input-group">
                    <i class="fas fa-map-marker-alt input-icon"></i>
                    <select class="form-select" id="locationSelect">
                        <option selected>Select Location</option>
                        <option value="new-york">New York</option>
                        <option value="los-angeles">Los Angeles</option>
                        <option value="chicago">Chicago</option>
                        <option value="houston">Houston</option>
                        <option value="phoenix">Phoenix</option>
                    </select>
                </div>
                <div class="search-input-group">
                    <i class="fas fa-calendar-alt input-icon"></i>
                    <input type="date" class="form-control" id="dateSelect" placeholder="Select Date">
                </div>
                <button type="submit" class="btn btn-search">Search Place</button>
            </form>
            <div class="d-flex align-items-center justify-content-center gap-3">
                <i class="fa-solid fa-play text-white"></i>
                <p class="text-white">Take virtual tour of our spaces</p>
            </div>
        </div>
    </div>
    <div class="stats d-flex align-items-center justify-content-center text-center gap-5 mb-5 mt-5">
        <div class="stat-1">
            <h2 class="mb-3">06</h2>
            <p>Offices are available on different countries</p>
        </div>
        <div class="stat-2">
            <h2 class="mb-3">238</h2>
            <p>Seats are available right now with dedicated support</p>
        </div>
        <div class="stat-3">
            <h2 class="mb-3">1,395</h2>
            <p>People are using our co-work spaces right now</p>
        </div>
    </div>
    <div class="popular-locations py-5">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center mb-5">
                    <h2 class="section-title mb-3">Popular locations</h2>
                    <p class="section-subtitle text-muted">With lots of unique blocks, you can easily build a page<br>easily without any coding.</p>
                </div>
            </div>
            <div class="row g-4">
                <!-- Beauview Location -->
                <div class="col-lg-4 col-md-6">
                    <div class="location-card">
                        <div class="location-image">
                            <img src="assets/loc-1.png" alt="Beauview Office Space" class="img-fluid rounded">
                        </div>
                        <div class="location-content text-center mt-3">
                            <h4 class="location-name mb-2">Beauview</h4>
                            <p class="location-seats text-muted">37 seats</p>
                        </div>
                    </div>
                </div>

                <!-- Haleyborough Location -->
                <div class="col-lg-4 col-md-6">
                    <div class="location-card">
                        <div class="location-image">
                            <img src="assets/loc-2.png" alt="Haleyborough Office Space" class="img-fluid rounded">
                        </div>
                        <div class="location-content text-center mt-3">
                            <h4 class="location-name mb-2">Haleyborough</h4>
                            <p class="location-seats text-muted">12 seats</p>
                        </div>
                    </div>
                </div>

                <!-- Jeromyshire Location -->
                <div class="col-lg-4 col-md-6">
                    <div class="location-card">
                        <div class="location-image">
                            <img src="assets/loc-3.png" alt="Jeromyshire Office Space" class="img-fluid rounded">
                        </div>
                        <div class="location-content text-center mt-3">
                            <h4 class="location-name mb-2">Jeromyshire</h4>
                            <p class="location-seats text-muted">28 seats</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="talented-people py-5">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <div class="content-wrapper pe-lg-4">
                        <h2 class="section-title mb-4">Work around very talented people.</h2>
                        <p class="section-description text-muted mb-4">With lots of unique blocks, you can easily build a page easily without any coding.</p>

                        <div class="bottom-image mt-4">
                            <img src="assets/talent-02.png" alt="Person working on laptop" class="img-fluid rounded">
                        </div>
                    </div>
                </div>

                <div class="col-lg-6">
                    <div class="right-image-wrapper ps-lg-4">
                        <img src="assets/talent-01.png" alt="People working together" class="img-fluid rounded">
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Support Section -->
    <section class="support-section py-5">
        <div class="container">
            <div class="row align-items-center">
                <!-- Left Content -->
                <div class="col-lg-6">
                    <div class="support-content pe-lg-5">
                        <h2 class="support-title mb-4">We are always here for your backup.</h2>
                        <p class="support-description mb-5">We share common trends and strategies for creating & improving your rental income.</p>

                        <!-- Features List -->
                        <div class="features-list">
                            <div class="feature-item d-flex align-items-center mb-4">
                                <div class="feature-icon me-3">
                                    <i class="fas fa-check-circle"></i>
                                </div>
                                <div class="feature-content">
                                    <h5 class="feature-title mb-2">Noise Free Locations</h5>
                                    <p class="feature-description mb-0">With lots of unique blocks, you can easily build a page without coding.</p>
                                </div>
                            </div>

                            <div class="feature-item d-flex align-items-center">
                                <div class="feature-icon me-3">
                                    <i class="fas fa-check-circle"></i>
                                </div>
                                <div class="feature-content">
                                    <h5 class="feature-title mb-2">24/7 Hour Support</h5>
                                    <p class="feature-description mb-0">With lots of unique blocks, you can easily build a page without coding.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Right FAQ Accordion -->
                <div class="col-lg-6">
                    <div class="faq-wrapper ps-lg-4">
                        <div class="accordion" id="faqAccordion">
                            <!-- FAQ Item 1 -->
                            <div class="accordion-item">
                                <h2 class="accordion-header" id="faq1">
                                    <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapse1" aria-expanded="true" aria-controls="collapse1">
                                        How to setup Shade Pro?
                                    </button>
                                </h2>
                                <div id="collapse1" class="accordion-collapse collapse show" aria-labelledby="faq1" data-bs-parent="#faqAccordion">
                                    <div class="accordion-body">
                                        With lots of unique blocks, you can easily build a page with coding. Build your next landing page. Integer ut Oberyn massa. Sed feugiat vitae turpis a porta.
                                    </div>
                                </div>
                            </div>

                            <!-- FAQ Item 2 -->
                            <div class="accordion-item">
                                <h2 class="accordion-header" id="faq2">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse2" aria-expanded="false" aria-controls="collapse2">
                                        Can I use Shade Pro for my clients?
                                    </button>
                                </h2>
                                <div id="collapse2" class="accordion-collapse collapse" aria-labelledby="faq2" data-bs-parent="#faqAccordion">
                                    <div class="accordion-body">
                                        With lots of unique blocks, you can easily build a page without coding. Build your next landing page.
                                    </div>
                                </div>
                            </div>

                            <!-- FAQ Item 3 -->
                            <div class="accordion-item">
                                <h2 class="accordion-header" id="faq3">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse3" aria-expanded="false" aria-controls="collapse3">
                                        How often do you release update?
                                    </button>
                                </h2>
                                <div id="collapse3" class="accordion-collapse collapse" aria-labelledby="faq3" data-bs-parent="#faqAccordion">
                                    <div class="accordion-body">
                                        With lots of unique blocks, you can easily build a page without coding. Build your next landing page.
                                    </div>
                                </div>
                            </div>

                            <!-- FAQ Item 4 -->
                            <div class="accordion-item">
                                <h2 class="accordion-header" id="faq4">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse4" aria-expanded="false" aria-controls="collapse4">
                                        How can I access to old version?
                                    </button>
                                </h2>
                                <div id="collapse4" class="accordion-collapse collapse" aria-labelledby="faq4" data-bs-parent="#faqAccordion">
                                    <div class="accordion-body">
                                        With lots of unique blocks, you can easily build a page without coding. Build your next landing page.
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</main>
<script src="js/bootstrap.bundle.min.js"></script>
<script src="js/all.min.js"></script>
<script>
// Header scroll effect
window.addEventListener('scroll', function() {
    const navbar = document.getElementById('mainNavbar');
    if (window.scrollY > 50) {
        navbar.classList.add('navbar-scrolled');
    } else {
        navbar.classList.remove('navbar-scrolled');
    }
});
</script>
</body>
</html>