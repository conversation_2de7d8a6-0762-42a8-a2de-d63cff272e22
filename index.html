<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport"
          content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
  <link rel="stylesheet" href="css/bootstrap.min.css">
  <link rel="stylesheet" href="css/all.min.css">
  <link rel="stylesheet" href="css/style.css">
    <title>Brainwave</title>
</head>
<body>
<header>
    <nav class="navbar navbar-expand-lg navbar-transparent" id="mainNavbar">
        <div class="container">
            <a class="navbar-brand" href="#"><img src="assets/logo.svg" alt=""></a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNavAltMarkup" aria-controls="navbarNavAltMarkup" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNavAltMarkup">
                <div class="navbar-nav ms-auto mb-2 mb-lg-0 text-white">
                    <a class="nav-link p-2 p-lg-3" aria-current="page" href="#">Home</a>
                    <a class="nav-link p-2 p-lg-3" href="#">Features</a>
                    <a class="nav-link p-2 p-lg-3" href="#">Pricing</a>
                    <a class="nav-link p-2 p-lg-3" aria-disabled="true">Disabled</a>
                </div>
            </div>
        </div>
    </nav>
</header>
<main>
    <div class="hero-section d-flex align-items-center justify-content-center">
        <div class="hero-content text-center">
            <h6>Shared space in your town</h6>
            <h1>Rent desk space in a shared office environment</h1>
            <form class="search-form d-flex align-items-center justify-content-center gap-3 mb-4">
                <div class="search-input-group">
                    <i class="fas fa-map-marker-alt input-icon"></i>
                    <select class="form-select" id="locationSelect">
                        <option selected>Select Location</option>
                        <option value="new-york">New York</option>
                        <option value="los-angeles">Los Angeles</option>
                        <option value="chicago">Chicago</option>
                        <option value="houston">Houston</option>
                        <option value="phoenix">Phoenix</option>
                    </select>
                </div>
                <div class="search-input-group">
                    <i class="fas fa-calendar-alt input-icon"></i>
                    <input type="date" class="form-control" id="dateSelect" placeholder="Select Date">
                </div>
                <button type="submit" class="btn btn-search">Search Place</button>
            </form>
            <div class="d-flex align-items-center justify-content-center gap-3">
                <i class="fa-solid fa-play text-white"></i>
                <p class="text-white">Take virtual tour of our spaces</p>
            </div>
        </div>
    </div>
    <div class="stats d-flex align-items-center justify-content-center text-center gap-5 mb-5 mt-5">
        <div class="stat-1">
            <h2 class="mb-3">06</h2>
            <p>Offices are available on different countries</p>
        </div>
        <div class="stat-2">
            <h2 class="mb-3">238</h2>
            <p>Seats are available right now with dedicated support</p>
        </div>
        <div class="stat-3">
            <h2 class="mb-3">1,395</h2>
            <p>People are using our co-work spaces right now</p>
        </div>
    </div>
</main>
<script src="js/bootstrap.bundle.min.js"></script>
<script src="js/all.min.js"></script>
<script>
// Header scroll effect
window.addEventListener('scroll', function() {
    const navbar = document.getElementById('mainNavbar');
    if (window.scrollY > 50) {
        navbar.classList.add('navbar-scrolled');
    } else {
        navbar.classList.remove('navbar-scrolled');
    }
});
</script>
</body>
</html>