
/* Comprehensive CSS Reset */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    margin: 0;
    padding: 0;
    height: 100%;
}

body {
    margin: 0 !important;
    padding: 0 !important;
    height: 100%;
    line-height: 1;
}

/* Header Styles */
.navbar-transparent {
    background-color: transparent !important;
    transition: background-color 0.3s ease;
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
}

.navbar-scrolled {
    background-color: #161C2D !important;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* Navbar link styles */
.navbar-transparent .navbar-nav .nav-link {
    color: white !important;
}

.navbar-transparent .navbar-brand {
    color: white !important;
}

.navbar-transparent .navbar-toggler {
    border-color: white;
}

.navbar-transparent .navbar-toggler-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 1%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}


.hero-section{
    background: url("../assets/hero.png") no-repeat center center/cover;
    min-height: 100vh;
    position: relative;
}

.hero-content {
    position: relative;
    z-index: 2;
    max-width: 800px;
    width: 100%;
    padding: 20px;
}

.hero-content h6 {
    color: #68D585;
    font-size: 1rem;
    margin-bottom: 10px;
    font-weight: 400;
    letter-spacing: 5px;
}

.hero-content h1 {
    color: white;
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 30px;
    line-height: 1.2;
}

/* Search Form Styles */
.search-form {
    background: white;
    padding: 15px;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    max-width: 700px;
    margin: 0 auto;
    flex-wrap: nowrap;
}

.search-input-group {
    position: relative;
    flex: 1;
    min-width: 180px;
}

.search-input-group .input-icon {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #666;
    font-size: 14px;
    z-index: 10;
    pointer-events: none;
}

.search-input-group .form-select,
.search-input-group .form-control {
    padding-left: 45px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    height: 50px;
    font-size: 14px;
    background-color: #f8f9fa;
    position: relative;
}

.search-input-group .form-select:focus,
.search-input-group .form-control:focus {
    border-color: #6366f1;
    box-shadow: 0 0 0 0.2rem rgba(99, 102, 241, 0.25);
    background-color: white;
}

.btn-search {
    background-color: #6366f1;
    color: white;
    border: none;
    padding: 12px 25px;
    border-radius: 8px;
    font-weight: 600;
    height: 50px;
    min-width: 130px;
    flex-shrink: 0;
    transition: background-color 0.3s ease;
    white-space: nowrap;
}

.btn-search:hover {
    background-color: #5855eb;
    color: white;
}

/* Virtual tour section */
.d-flex p {
    margin-bottom: 0;
    white-space: nowrap;
}

.d-flex i {
    flex-shrink: 0;
}

@media (max-width: 768px) {
    .search-form {
        flex-direction: column;
        gap: 15px !important;
    }

    .search-input-group {
        min-width: 100%;
    }

    .hero-content h1 {
        font-size: 2rem;
    }
}

.hero-section::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #161C2D;
    opacity: 0.7;
    z-index: 1;
}

/* Fix for consistent image heights and centering */
.location-image {
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
}

.location-image img {
    height: 500px;
    width: 100%;
    object-fit: cover;
    object-position: center;
}

.location-content {
    text-align: center;
}

/* Talented People Section */
.talented-people {
    background-color: #f8f9fa;
}

.talented-people .section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2c3e50;
    line-height: 1.2;
}

.talented-people .section-description {
    font-size: 1.1rem;
    line-height: 1.6;
    color: #6c757d;
}

.talented-people .bottom-image img {
    max-height: 600px;
    object-fit: cover;
    width: 100%;
}

.talented-people .right-image-wrapper img {
    max-height: 600px;
    object-fit: cover;
    width: 100%;
}

/* Responsive adjustments */
@media (max-width: 991.98px) {
    .talented-people .section-title {
        font-size: 2rem;
        text-align: center;
    }

    .talented-people .section-description {
        text-align: center;
    }

    .talented-people .right-image-wrapper {
        margin-top: 2rem;
    }
}

/* Support Section */
.support-section {
    background-color: #161C2D;
    color: white;
    padding: 100px 0 !important;
    min-height: 80vh;
    display: flex;
    align-items: center;
}

.support-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: white;
    line-height: 1.2;
}

.support-description {
    font-size: 1.1rem;
    color: #bdc3c7;
    line-height: 1.6;
}

/* Features List */
.feature-item {
    margin-bottom: 2rem;
}

.feature-icon {
    flex-shrink: 0;
}

.feature-icon i {
    color: #27ae60;
    font-size: 1.5rem;
}

.feature-title {
    color: white;
    font-weight: 600;
    font-size: 1.2rem;
}

.feature-description {
    color: #bdc3c7;
    font-size: 0.95rem;
    line-height: 1.5;
}

/* FAQ Accordion Styling */
.faq-wrapper .accordion {
    background: transparent;
    border-top: 4px solid #27ae60;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 15px rgba(0,0,0,0.08);
}

.faq-wrapper .accordion-item {
    background-color: white;
    border: none;
    border-radius: 0 !important;
    margin-bottom: 0;
    box-shadow: none;
    overflow: hidden;
}

.faq-wrapper .accordion-item:first-child .accordion-button {
    border-radius: 8px 8px 0 0 !important;
}

.faq-wrapper .accordion-item:last-child .accordion-button {
    border-radius: 0 0 8px 8px !important;
}

.faq-wrapper .accordion-item:last-child .accordion-collapse {
    border-radius: 0 0 8px 8px !important;
}

.faq-wrapper .accordion-item:not(:last-child) {
    border-bottom: 1px solid #e9ecef;
}

.faq-wrapper .accordion-button {
    background-color: white;
    color: #2c3e50;
    font-weight: 600;
    border: none;
    padding: 1.5rem 2rem;
    border-radius: 0 !important;
    box-shadow: none;
    font-size: 1.1rem;
}

.faq-wrapper .accordion-button:not(.collapsed) {
    background-color: white;
    color: #2c3e50;
    box-shadow: none;
    border-bottom: 1px solid #e9ecef;
}

.faq-wrapper .accordion-button:focus {
    box-shadow: none;
    border: none;
}

.faq-wrapper .accordion-button::after {
    color: #2c3e50;
    font-size: 1.2rem;
    font-weight: bold;
}

.faq-wrapper .accordion-body {
    padding: 1.5rem 2rem 2rem 2rem;
    color: #6c757d;
    line-height: 1.7;
    font-size: 1rem;
    background-color: #f8f9fa;
}

.faq-wrapper .accordion-collapse {
    border-radius: 0;
}

/* Responsive adjustments for support section */
@media (max-width: 991.98px) {
    .support-title {
        font-size: 2rem;
        text-align: center;
    }

    .support-description {
        text-align: center;
    }

    .faq-wrapper {
        margin-top: 3rem;
    }

    .feature-item {
        justify-content: center;
        text-align: center;
    }

    .feature-content {
        text-align: center;
    }
}

/* Newsletter Section */
.newsletter-section {
    background-color: #f8f9fa;
    padding: 80px 0;
}

.newsletter-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 1.5rem;
}

.newsletter-description {
    font-size: 1.1rem;
    color: #6c757d;
    line-height: 1.6;
    margin-bottom: 2rem;
}

.newsletter-input {
    padding: 15px 20px;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    font-size: 1rem;
    background-color: white;
}

.newsletter-input:focus {
    border-color: #5a67d8;
    box-shadow: 0 0 0 0.2rem rgba(90, 103, 216, 0.25);
}

.newsletter-btn {
    background-color: #5a67d8;
    border: none;
    padding: 15px 30px;
    border-radius: 8px;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.newsletter-btn:hover {
    background-color: #4c51bf;
    transform: translateY(-2px);
}

.newsletter-privacy {
    font-size: 0.9rem;
    color: #6c757d;
    margin-bottom: 0;
}

.privacy-link {
    color: #5a67d8;
    text-decoration: none;
}

.privacy-link:hover {
    color: #4c51bf;
    text-decoration: underline;
}

/* Footer Section */
.footer-section {
    background-color: white;
    border-top: 1px solid #e9ecef;
    padding: 60px 0 40px 0;
}

.footer-heading {
    font-size: 1rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 1.5rem;
}

.footer-links {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-links li {
    margin-bottom: 0.75rem;
}

.footer-links a {
    color: #6c757d;
    text-decoration: none;
    font-size: 0.95rem;
    transition: color 0.3s ease;
}

.footer-links a:hover {
    color: #5a67d8;
}

.contact-email a {
    color: #5a67d8;
    text-decoration: none;
    font-weight: 500;
}

.contact-email a:hover {
    color: #4c51bf;
    text-decoration: underline;
}

.contact-phone {
    color: #2c3e50;
    font-weight: 600;
    font-size: 1.1rem;
}

.footer-copyright {
    color: #6c757d;
    font-size: 0.9rem;
}

.social-links {
    display: flex;
    align-items: center;
    justify-content: flex-end;
}

.social-link {
    color: #6c757d;
    font-size: 1.2rem;
    transition: color 0.3s ease;
    text-decoration: none;
}

.social-link:hover {
    color: #5a67d8;
}

/* Responsive adjustments for newsletter and footer */
@media (max-width: 991.98px) {
    .newsletter-title {
        font-size: 2rem;
    }

    .social-links {
        justify-content: center;
        margin-top: 1rem;
    }

    .footer-copyright {
        text-align: center;
    }
}
